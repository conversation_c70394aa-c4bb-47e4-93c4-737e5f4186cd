{"version": 3, "file": "notarytool.js", "sourceRoot": "", "sources": ["../src/notarytool.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,2CAA6B;AAE7B,mCAAgC;AAChC,uCAAoD;AACpD,mDAIyB;AAGzB,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,8BAA8B,CAAC,CAAC;AAEhD,SAAS,aAAa,CAAC,IAAc,EAAE,cAAuB;IAC5D,MAAM,QAAQ,GAAG,cAAc,KAAK,SAAS,CAAC;IAC9C,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC;IAChD,OAAO,IAAA,aAAK,EAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,iBAAiB,CAAC,OAA8B;IACvD,MAAM,IAAI,GAAG,IAAA,mDAAmC,EAAC,OAAO,CAAC,CAAC;IAC1D,IAAI,IAAA,+CAA+B,EAAC,IAAI,CAAC,EAAE;QACzC,OAAO;YACL,YAAY;YACZ,IAAA,oBAAU,EAAC,IAAI,CAAC,OAAO,CAAC;YACxB,YAAY;YACZ,IAAA,oBAAU,EAAC,IAAI,CAAC,eAAe,CAAC;YAChC,WAAW;YACX,IAAA,oBAAU,EAAC,IAAI,CAAC,MAAM,CAAC;SACxB,CAAC;KACH;SAAM,IAAI,IAAA,6CAA6B,EAAC,IAAI,CAAC,EAAE;QAC9C,OAAO;YACL,OAAO;YACP,IAAA,oBAAU,EAAC,IAAI,CAAC,WAAW,CAAC;YAC5B,UAAU;YACV,IAAA,oBAAU,EAAC,IAAI,CAAC,aAAa,CAAC;YAC9B,UAAU;YACV,IAAA,oBAAU,EAAC,IAAI,CAAC,cAAc,CAAC;SAChC,CAAC;KACH;SAAM;QACL,0FAA0F;QAC1F,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SAClF;QACD,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;KACrD;AACH,CAAC;AAED,SAAe,mBAAmB,CAAC,IAA4B,EAAE,EAAU;;QACzE,IAAI;YACF,MAAM,SAAS,GAAG,MAAM,aAAa,CACnC,CAAC,KAAK,EAAE,EAAE,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,EACvC,IAAI,CAAC,cAAc,CACpB,CAAC;YACF,CAAC,CAAC,kBAAkB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YACxC,OAAO,SAAS,CAAC,MAAM,CAAC;SACzB;QAAC,OAAO,CAAC,EAAE;YACV,CAAC,CAAC,kCAAkC,EAAE,CAAC,CAAC,CAAC;SAC1C;IACH,CAAC;CAAA;AAED,SAAsB,qBAAqB,CAAC,cAAuB;;QACjE,IAAI,cAAc,KAAK,SAAS,EAAE;YAChC,MAAM,MAAM,GAAG,MAAM,IAAA,aAAK,EAAC,cAAc,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;YAC1D,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC;SAC1B;aAAM;YACL,MAAM,MAAM,GAAG,MAAM,IAAA,aAAK,EAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;YAC9D,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC;SAC1B;IACH,CAAC;CAAA;AARD,sDAQC;AAED,SAAsB,4BAA4B,CAAC,IAA4B;;QAC7E,CAAC,CAAC,oCAAoC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACtD,OAAO,MAAM,IAAA,qBAAW,EAAC,CAAM,GAAG,EAAC,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3C,IAAI,QAAQ,CAAC;YACb,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,EAAE;gBAC5C,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC3C,CAAC,CAAC,sCAAsC,EAAE,QAAQ,CAAC,CAAC;aACrD;iBAAM;gBACL,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;gBACrE,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;gBACvC,MAAM,SAAS,GAAG,MAAM,IAAA,aAAK,EAC3B,OAAO,EACP,CAAC,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,EACtF;oBACE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;iBAChC,CACF,CAAC;gBACF,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;oBACxB,MAAM,IAAI,KAAK,CACb,gDAAgD,SAAS,CAAC,IAAI,OAAO,SAAS,CAAC,MAAM,EAAE,CACxF,CAAC;iBACH;gBACD,CAAC,CAAC,8CAA8C,CAAC,CAAC;aACnD;YAED,MAAM,YAAY,GAAG;gBACnB,QAAQ;gBACR,QAAQ;gBACR,GAAG,iBAAiB,CAAC,IAAI,CAAC;gBAC1B,QAAQ;gBACR,iBAAiB;gBACjB,MAAM;aACP,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACtE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAEpC,IAAI,MAAW,CAAC;YAChB,IAAI;gBACF,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;aAC7B;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,IAAI,KAAK,CACb,0EAA0E,MAAM,EAAE,CACnF,CAAC;aACH;YAED,IAAI,SAA6B,CAAC;YAClC,IAAI,OAAO,MAAM,CAAC,EAAE,KAAK,QAAQ,EAAE;gBACjC,SAAS,GAAG,MAAM,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;aACxD;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;gBACrD,CAAC,CAAC,6BAA6B,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC7C,OAAO;aACR;YAED,IAAI,OAAO,GAAG,wCAAwC,MAAM,CAAC,MAAM,EAAE,CAAC;YACtE,IAAI,SAAS,EAAE;gBACb,OAAO,IAAI,wCAAwC,SAAS,EAAE,CAAC;aAChE;YACD,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC,CAAA,CAAC,CAAC;IACL,CAAC;CAAA;AA/DD,oEA+DC"}