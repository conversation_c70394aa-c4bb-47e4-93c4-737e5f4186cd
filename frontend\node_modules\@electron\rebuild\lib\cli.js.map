{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,kDAA0B;AAC1B,6CAA+B;AAC/B,2CAA6B;AAC7B,2BAA4B;AAC5B,wDAAgC;AAEhC,mDAAqD;AACrD,yDAA0D;AAE1D,uCAAoC;AAEpC,MAAM,IAAI,GAAG,IAAA,eAAK,EAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;IAC/D,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0CAA0C,EAAE;IAChG,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,8DAA8D,EAAE;IACnH,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,wEAAwE,EAAE;IAC3H,YAAY,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,mDAAmD,EAAE;IAC9G,8BAA8B;IAC9B,cAAc,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,wKAAwK,EAAE;IACrO,8BAA8B;IAC9B,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0FAA0F,EAAE;IAC7I,uBAAuB,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0CAA0C,EAAE;IAChH,UAAU,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,2BAA2B,EAAE;IACpF,8BAA8B;IAC9B,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,0HAA0H,EAAE;IAC9K,QAAQ,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,oEAAoE,EAAE;IAC5H,UAAU,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,qEAAqE,EAAE;IAC/H,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,gCAAgC,EAAE;IACrF,qBAAqB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,8DAA8D,EAAE;IACtH,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,oHAAoH,EAAE;IAClK,oBAAoB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,kHAAkH,EAAE;IAC1K,sBAAsB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,gCAAgC,EAAE;IAC1F,mBAAmB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,0DAA0D,EAAC;CACjH,CAAC,CAAC,KAAK,CAAC,mDAAmD,CAAC;KAC1D,IAAI,EAAE;KACN,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC;KAClB,MAAM,CAAC,qBAAqB,CAAC;KAC7B,SAAS,EAAE,CAAC;AAEf,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;IAChE,uDAAuD;IACvD,IAAI;QACF,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;KAC1G;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;KACvG;IACD,sDAAsD;IACtD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACjB;AAED,MAAM,OAAO,GAAG,CAAC,GAAU,EAAQ,EAAE;IACnC,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC,CAAC;IAClF,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC3D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC,CAAC;AAEF,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;AACzC,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;AAG1C,CAAC,KAAK,IAAmB,EAAE;IACzB,MAAM,eAAe,GAAG,MAAM,IAAA,kCAAkB,EAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAChE,MAAM,kBAAkB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAG,IAAI,CAAC,CAAY,CAAC,CAAC,CAAC,CAAC,MAAM,IAAA,uCAAoB,EAAC,eAAe,CAAC,CAAC;IAClI,IAAI,qBAAqB,GAAG,IAAI,CAAC,CAAW,CAAC;IAE7C,IAAI,CAAC,qBAAqB,EAAE;QAC1B,IAAI;YACF,IAAI,CAAC,kBAAkB;gBAAE,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAC/E,8DAA8D;YAC9D,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC,CAAC;YAEvE,qBAAqB,GAAG,OAAO,CAAC,OAAO,CAAC;SACzC;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,4FAA4F,CAAC,CAAC;SAC/G;KACF;IAED,IAAI,aAAa,GAAG,IAAI,CAAC,CAAW,CAAC;IAErC,IAAI,CAAC,aAAa,EAAE;QAClB,wEAAwE;QACxE,uEAAuE;QACvE,aAAa;QACb,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE;YAC5G,sBAAsB;YACtB,aAAa,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE;gBAC5G,MAAM,IAAI,KAAK,CAAC,4HAA4H,CAAC,CAAC;aAC/I;SACF;KACF;SAAM;QACL,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,aAAa,CAAC,CAAC;KAC5D;IAED,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;QACtD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;KAC/C;IAED,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,MAAM,cAAc,GAAG,GAAG,CAAC,2BAA2B,CAAC,CAAC,KAAK,EAAE,CAAC;IAChE,IAAI,cAAsB,CAAC;IAE3B,MAAM,MAAM,GAAG,CAAC,UAAmB,EAAQ,EAAE;QAC3C,IAAI,UAAU;YAAE,cAAc,GAAG,UAAU,CAAC;QAE5C,IAAI,IAAI,CAAC,CAAC,EAAE;YACV,cAAc,CAAC,IAAI,GAAG,qBAAqB,WAAW,IAAI,WAAW,EAAE,CAAC;SACzE;aAAM;YACL,cAAc,CAAC,IAAI,GAAG,oBAAoB,cAAc,gBAAgB,WAAW,EAAE,CAAC;SACvF;IACH,CAAC,CAAC;IAEF,MAAM,SAAS,GAAG,IAAA,iBAAO,EAAC;QACxB,SAAS,EAAE,aAAa;QACxB,eAAe,EAAE,qBAAqB;QACtC,IAAI,EAAG,IAAI,CAAC,CAAY,IAAI,OAAO,CAAC,IAAI;QACxC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAE,IAAI,CAAC,CAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;QACzD,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAE,IAAI,CAAC,CAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QAC1D,KAAK,EAAE,IAAI,CAAC,CAAY;QACxB,SAAS,EAAE,IAAI,CAAC,CAAW;QAC3B,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAE,IAAI,CAAC,CAAY,CAAC,KAAK,CAAC,GAAG,CAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC;QACpF,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/D,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,iBAAiB,EAAG,IAAI,CAAC,iBAA4B,IAAI,GAAG;QAC5D,QAAQ,EAAE,IAAI,CAAC,QAAkB;QACjC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB;QACzC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB;QAC3C,eAAe;QACf,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,eAAe;KACxC,CAAC,CAAC;IAEH,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;IAEtC,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,UAAkB,EAAE,EAAE;QAClD,WAAW,IAAI,CAAC,CAAC;QACjB,MAAM,CAAC,UAAU,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE,GAAG,EAAE;QAC/B,WAAW,IAAI,CAAC,CAAC;QACjB,MAAM,EAAE,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,IAAI;QACF,MAAM,SAAS,CAAC;KACjB;IAAC,OAAO,GAAG,EAAE;QACZ,cAAc,CAAC,IAAI,GAAG,gBAAgB,CAAC;QACvC,cAAc,CAAC,IAAI,EAAE,CAAC;QACtB,MAAM,GAAG,CAAC;KACX;IAED,cAAc,CAAC,IAAI,GAAG,kBAAkB,CAAC;IACzC,cAAc,CAAC,OAAO,EAAE,CAAC;AAC3B,CAAC,CAAC,EAAE,CAAC"}