{"version": 3, "file": "asar-utils.js", "sourceRoot": "", "sources": ["../../src/asar-utils.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAAkC;AAClC,iDAA6C;AAC7C,oDAA4B;AAC5B,wDAA0B;AAC1B,gDAAwB;AACxB,yCAAsC;AACtC,4CAAoB;AACpB,mCAA4B;AAE5B,MAAM,IAAI,GAAG,MAAM,CAAC;AAEpB,IAAY,QAGX;AAHD,WAAY,QAAQ;IAClB,6CAAO,CAAA;IACP,+CAAQ,CAAA;AACV,CAAC,EAHW,QAAQ,wBAAR,QAAQ,QAGnB;AAUD,qJAAqJ;AACrJ,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC;IAC1B,gBAAgB;IAChB,UAAU,EAAE,UAAU;IAEtB,gBAAgB;IAChB,UAAU,EAAE,UAAU;CACvB,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAAC;IACpC,YAAY;IACZ,UAAU,EAAE,UAAU;CACvB,CAAC,CAAC;AAEI,MAAM,cAAc,GAAG,KAAK,EAAE,OAAe,EAAE,EAAE;IACtD,IAAA,SAAC,EAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;IACpC,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IAE5E,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE;QACpC,IAAA,SAAC,EAAC,oBAAoB,CAAC,CAAC;QACxB,OAAO,QAAQ,CAAC,OAAO,CAAC;KACzB;IAED,IAAA,SAAC,EAAC,qBAAqB,CAAC,CAAC;IACzB,OAAO,QAAQ,CAAC,QAAQ,CAAC;AAC3B,CAAC,CAAC;AAXW,QAAA,cAAc,kBAWzB;AAEK,MAAM,qBAAqB,GAAG,CAAC,QAAgB,EAAE,EAAE;IACxD,OAAO;QACL,SAAS,EAAE,QAAiB;QAC5B,IAAI,EAAE,gBAAM;aACT,UAAU,CAAC,QAAQ,CAAC;aACpB,MAAM,CAAC,cAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC;aAChD,MAAM,CAAC,KAAK,CAAC;KACjB,CAAC;AACJ,CAAC,CAAC;AARW,QAAA,qBAAqB,yBAQhC;AAEF,SAAS,cAAc,CAAC,IAAY;IAClC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,WAAW,CAAC,CAAS,EAAE,IAAY;IAC1C,OAAO,OAAO,CAAC,OAAO,IAAI,cAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACpD,CAAC;AAED,SAAS,eAAe,CAAC,OAAe,EAAE,IAAY,EAAE,SAAkB;IACxE,IAAI,SAAS,KAAK,SAAS,IAAI,CAAC,IAAA,qBAAS,EAAC,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE;QAC/E,MAAM,IAAI,KAAK,CACb,yBAAyB,IAAI,SAAS,OAAO,mBAAmB;YAC9D,oBAAoB,SAAS,GAAG,CACnC,CAAC;KACH;AACH,CAAC;AAEM,MAAM,UAAU,GAAG,KAAK,EAAE,EAC/B,WAAW,EACX,aAAa,EACb,cAAc,EACd,eAAe,GACG,EAAiB,EAAE;IACrC,IAAA,SAAC,EAAC,WAAW,WAAW,QAAQ,aAAa,EAAE,CAAC,CAAC;IAEjD,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,cAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;IAC5E,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,cAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;IAEhF,EAAE;IACF,8CAA8C;IAC9C,EAAE;IAEF,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;IAExC,SAAS,aAAa,CAAC,CAAS,EAAE,QAAqB;QACrD,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;YAC3B,MAAM,IAAI,GAAG,cAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAEpC,IAAI,CAAC,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAC3C,SAAS;aACV;YAED,IAAI,OAAO,IAAI,IAAI,EAAE;gBACnB,SAAS;aACV;YACD,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACzB;IACH,CAAC;IAED,aAAa,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IACrC,aAAa,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IAEzC,EAAE;IACF,sDAAsD;IACtD,EAAE;IAEF,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;QAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACzB,eAAe,CAAC,WAAW,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;SACrD;KACF;IACD,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE;QAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACvB,eAAe,CAAC,aAAa,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;YACtD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACxB;KACF;IAED,EAAE;IACF,8CAA8C;IAC9C,EAAE;IAEF,MAAM,cAAc,GAAG,EAAE,CAAC;IAC1B,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;QAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACzB,SAAS;SACV;QAED,mBAAmB;QACnB,IAAI,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE;YAClC,SAAS;SACV;QAED,MAAM,UAAU,GAAG,cAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACvD,MAAM,YAAY,GAAG,cAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAE3D,IAAI,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YAC1C,SAAS;SACV;QAED,IACE,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrD,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EACvD;YACA,SAAS;SACV;QAED,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;YAChD,MAAM,IAAI,KAAK,CAAC,uCAAuC,IAAI,EAAE,CAAC,CAAC;SAChE;QAED,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC3B;IAED,EAAE;IACF,eAAe;IACf,EAAE;IAEF,MAAM,MAAM,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,YAAE,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IAChE,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,YAAE,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEpE,IAAI;QACF,IAAA,SAAC,EAAC,cAAc,WAAW,OAAO,MAAM,EAAE,CAAC,CAAC;QAC5C,cAAI,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAErC,IAAA,SAAC,EAAC,cAAc,aAAa,OAAO,QAAQ,EAAE,CAAC,CAAC;QAChD,cAAI,CAAC,UAAU,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAEzC,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;YAC9B,MAAM,MAAM,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC5C,MAAM,WAAW,GAAG,cAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAE/C,IAAI,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE;gBACpC,IAAA,SAAC,EAAC,8BAA8B,IAAI,EAAE,CAAC,CAAC;gBACxC,MAAM,kBAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAC7B,SAAS;aACV;YAED,IAAA,SAAC,EAAC,wBAAwB,IAAI,EAAE,CAAC,CAAC;YAClC,MAAM,kBAAE,CAAC,MAAM,CAAC,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;YAC3C,MAAM,kBAAE,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;SACpC;QAED,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE;YACpC,MAAM,MAAM,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;YAClE,MAAM,WAAW,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;YAErE,IAAA,SAAC,EAAC,oBAAoB,OAAO,EAAE,CAAC,CAAC;YACjC,IAAA,4BAAY,EAAC,IAAI,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;SAC9E;QAED,IAAA,SAAC,EAAC,uBAAuB,cAAc,EAAE,CAAC,CAAC;QAE3C,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;QAExF,IAAI,MAA0B,CAAC;QAC/B,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,MAAM,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;SAC1C;aAAM,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;YACtC,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;SAC5B;QAED,MAAM,cAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,cAAc,EAAE;YAC1D,MAAM;SACP,CAAC,CAAC;QAEH,IAAA,SAAC,EAAC,cAAc,CAAC,CAAC;KACnB;YAAS;QACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,kBAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC7D;AACH,CAAC,CAAC;AAhJW,QAAA,UAAU,cAgJrB"}