{"name": "restaurant-pos-frontend", "version": "1.0.0", "type": "module", "main": "dist-electron/main.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "start": "electron dist-electron/main.js", "pack": "electron-builder", "dist": "npm run build && electron-builder"}, "keywords": [], "author": "", "license": "ISC", "description": "Restaurant POS System Frontend", "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@electron-toolkit/utils": "^4.0.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "electron": "^37.2.1", "electron-builder": "^26.0.12", "electron-vite": "^4.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^6.3.5"}}