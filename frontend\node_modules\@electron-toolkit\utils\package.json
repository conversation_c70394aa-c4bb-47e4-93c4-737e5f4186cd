{"name": "@electron-toolkit/utils", "version": "4.0.0", "description": "Utils for Electron main process.", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "files": ["dist"], "author": "<PERSON><https://github.com/alex8088>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/alex8088/electron-toolkit.git", "directory": "packages/utils"}, "bugs": {"url": "https://github.com/alex8088/electron-toolkit/issues"}, "homepage": "https://github.com/alex8088/electron-toolkit/tree/master/packages/utils#readme", "keywords": ["electron", "toolkit", "main process"], "scripts": {"build": "unbuild"}, "peerDependencies": {"electron": ">=13.0.0"}}